#!/usr/bin/env python3
"""
Highlights Scoring Utility for Intelligent Video Highlights

This module implements the multi-component scoring system for video highlights:
- Q&A Detection (35%)
- Keyword Density using KeyBERT (25%)
- Emotion Intensity via sentiment analysis (25%)
- Novelty scoring (15%)
"""

import logging
import numpy as np
from typing import List, Dict, Any, Optional
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)

# Check for library availability without importing heavy dependencies
KEYBERT_AVAILABLE = False
TRANSFORMERS_AVAILABLE = False
SENTENCE_TRANSFORMERS_AVAILABLE = False
SKLEARN_AVAILABLE = False
OPENAI_AVAILABLE = False

try:
    import importlib.util

    # Check KeyBERT availability
    if importlib.util.find_spec("keybert") is not None:
        KEYBERT_AVAILABLE = True

    # Check Transformers availability
    if importlib.util.find_spec("transformers") is not None:
        TRANSFORMERS_AVAILABLE = True

    # Check SentenceTransformers availability
    if importlib.util.find_spec("sentence_transformers") is not None:
        SENTENCE_TRANSFORMERS_AVAILABLE = True

    # Check sklearn availability
    if importlib.util.find_spec("sklearn") is not None:
        SKLEARN_AVAILABLE = True

    # Check OpenAI availability
    if importlib.util.find_spec("openai") is not None:
        try:
            from config.settings import OPENAI_API_KEY
            OPENAI_AVAILABLE = True
        except ImportError:
            OPENAI_AVAILABLE = False

except Exception as e:
    logger.warning(f"Error checking library availability: {e}")

# Log availability status
logger.info(f"Library availability - KeyBERT: {KEYBERT_AVAILABLE}, Transformers: {TRANSFORMERS_AVAILABLE}, "
           f"SentenceTransformers: {SENTENCE_TRANSFORMERS_AVAILABLE}, sklearn: {SKLEARN_AVAILABLE}, "
           f"OpenAI: {OPENAI_AVAILABLE}")


class HighlightsScorer:
    """
    Advanced scoring system for video highlights extraction
    """

    def __init__(self, embedding_model: str = "all-MiniLM-L6-v2",
                 openai_enabled: bool = False, openai_model: str = "gpt-4o-mini"):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.embedding_model_name = embedding_model

        # OpenAI configuration
        self.openai_enabled = openai_enabled and OPENAI_AVAILABLE
        self.openai_model = openai_model
        self.openai_client = None

        # Initialize models
        self._init_models()

        # Scoring weights (maintain the same proportions)
        self.weights = {
            'qa_score': 0.35,
            'keyword_density': 0.25,
            'emotion_intensity': 0.25,
            'novelty': 0.15
        }

        # Quality filter thresholds
        self.quality_thresholds = {
            'min_loudness_lufs': -40.0,
            'min_asr_confidence': 0.85,
            'max_silence_gap': 2.0
        }

        # OpenAI rate limiting and caching
        self.openai_cache = {}
        self.openai_request_count = 0
        self.max_openai_requests_per_session = 100  # Limit to control costs

    def _init_models(self):
        """Initialize ML models with lazy loading"""
        # Set models to None - they will be loaded when first needed
        self.keybert_model = None
        self.sentiment_model = None
        self.embedding_model = None
        self.openai_client = None

        # Track initialization status
        self._keybert_initialized = False
        self._sentiment_initialized = False
        self._embedding_initialized = False
        self._openai_initialized = False

        self.logger.info("HighlightsScorer initialized with lazy loading")

    def _get_keybert_model(self):
        """Lazy load KeyBERT model"""
        if not self._keybert_initialized:
            self._keybert_initialized = True
            if KEYBERT_AVAILABLE:
                try:
                    from keybert import KeyBERT
                    self.keybert_model = KeyBERT(model=self.embedding_model_name)
                    self.logger.info("KeyBERT model initialized successfully")
                except Exception as e:
                    self.logger.warning(f"Failed to initialize KeyBERT: {e}")
        return self.keybert_model

    def _get_sentiment_model(self):
        """Lazy load sentiment analysis model"""
        if not self._sentiment_initialized:
            self._sentiment_initialized = True
            if TRANSFORMERS_AVAILABLE:
                try:
                    from transformers.pipelines import pipeline
                    self.sentiment_model = pipeline(
                        "sentiment-analysis",
                        model="cardiffnlp/twitter-roberta-base-sentiment-latest",
                        return_all_scores=True
                    )
                    self.logger.info("Sentiment analysis model initialized successfully")
                except Exception as e:
                    self.logger.warning(f"Failed to initialize sentiment model: {e}")
        return self.sentiment_model

    def _get_embedding_model(self):
        """Lazy load sentence transformer model"""
        if not self._embedding_initialized:
            self._embedding_initialized = True
            if SENTENCE_TRANSFORMERS_AVAILABLE:
                try:
                    from sentence_transformers import SentenceTransformer
                    self.embedding_model = SentenceTransformer(self.embedding_model_name)
                    self.logger.info("Sentence transformer model initialized successfully")
                except Exception as e:
                    self.logger.warning(f"Failed to initialize embedding model: {e}")
        return self.embedding_model

    def _get_openai_client(self):
        """Lazy load OpenAI client"""
        if not self._openai_initialized:
            self._openai_initialized = True
            if self.openai_enabled and OPENAI_AVAILABLE:
                try:
                    import openai
                    from config.settings import OPENAI_API_KEY
                    self.openai_client = openai.OpenAI(api_key=OPENAI_API_KEY)
                    self.logger.info("OpenAI client initialized successfully")
                except Exception as e:
                    self.logger.warning(f"Failed to initialize OpenAI client: {e}")
                    self.openai_enabled = False
        return self.openai_client

    def detect_qa_patterns(self, segments: List[Dict[str, Any]]) -> Dict[int, float]:
        """
        Detect Q&A patterns in transcript segments with enhanced question identification

        Args:
            segments: List of transcript segments with text and speaker info

        Returns:
            Dictionary mapping segment indices to Q&A scores (0-1)
        """
        qa_scores = {}

        for i, segment in enumerate(segments):
            text = segment.get('text', '').strip()
            speaker = segment.get('speaker', 'unknown')

            # Enhanced question detection patterns
            is_question = self._is_question_segment(text)

            qa_score = 0.0

            if is_question:
                # Look for answer in next 1-3 segments
                answer_score = self._find_answer_for_question(segments, i, speaker)
                qa_score = answer_score

                # Enhance with OpenAI if available and score is promising
                if self.openai_enabled and answer_score > 0.3:
                    answer_text = self._get_answer_text_for_question(segments, i, speaker)
                    if answer_text:
                        enhanced_score = self._enhance_qa_detection_with_openai(text, answer_text)
                        qa_score = max(qa_score, enhanced_score)

                if qa_score > 0.0:
                    self.logger.debug(f"Q&A pattern detected at segment {i}: {text[:50]}... (score: {qa_score:.2f})")

            qa_scores[i] = qa_score

        return qa_scores

    def _is_question_segment(self, text: str) -> bool:
        """
        Enhanced question detection using multiple patterns

        Args:
            text: Text to analyze

        Returns:
            True if text appears to be a question
        """
        text_lower = text.lower().strip()

        # Direct question markers
        if text.endswith('?'):
            return True

        # Question word starters
        question_starters = [
            'what', 'how', 'why', 'when', 'where', 'who', 'which', 'whose',
            'can you', 'could you', 'would you', 'will you', 'should you',
            'do you', 'did you', 'have you', 'are you', 'is it', 'was it',
            'does', 'did', 'can', 'could', 'would', 'will', 'should',
            'is there', 'are there', 'was there', 'were there'
        ]

        if any(text_lower.startswith(starter) for starter in question_starters):
            return True

        # Indirect question patterns
        indirect_patterns = [
            'tell me about', 'explain', 'describe', 'elaborate on',
            'what do you think', 'what are your thoughts', 'how do you feel',
            'what would you say', 'what would happen if', 'imagine if'
        ]

        if any(pattern in text_lower for pattern in indirect_patterns):
            return True

        # Rising intonation indicators (common in speech-to-text)
        if any(phrase in text_lower for phrase in ['right?', 'correct?', 'true?', 'agree?']):
            return True

        return False

    def _find_answer_for_question(self, segments: List[Dict[str, Any]],
                                 question_idx: int, question_speaker: str) -> float:
        """
        Find and score potential answers following a question (works without speaker diarization)

        Args:
            segments: All transcript segments
            question_idx: Index of the question segment
            question_speaker: Speaker who asked the question (may be 'unknown')

        Returns:
            Score indicating quality of Q&A pattern (0-1)
        """
        max_lookahead = min(3, len(segments) - question_idx - 1)
        best_score = 0.0

        for i in range(1, max_lookahead + 1):
            answer_idx = question_idx + i
            if answer_idx >= len(segments):
                break

            answer_segment = segments[answer_idx]
            answer_speaker = answer_segment.get('speaker', 'unknown')
            answer_text = answer_segment.get('text', '').strip()

            # Skip if very short response
            if len(answer_text) < 10:
                continue

            # Skip if same speaker (only when we have speaker info)
            if (question_speaker != 'unknown' and answer_speaker != 'unknown' and
                answer_speaker == question_speaker):
                continue

            # Score based on answer quality indicators
            score = 0.0

            # Different speaker responding = good (when we have speaker info)
            if (answer_speaker != 'unknown' and question_speaker != 'unknown' and
                answer_speaker != question_speaker):
                score += 0.4
            elif answer_speaker == 'unknown' or question_speaker == 'unknown':
                # Base score when no speaker info available
                score += 0.2

            # Answer length indicates substantive response (more important without speaker info)
            if len(answer_text) > 50:
                score += 0.4
            elif len(answer_text) > 30:
                score += 0.3
            elif len(answer_text) > 15:
                score += 0.2

            # Answer starts with typical response patterns
            answer_lower = answer_text.lower()
            response_starters = [
                'yes', 'no', 'well', 'so', 'actually', 'i think', 'i believe',
                'absolutely', 'definitely', 'certainly', 'of course', 'sure',
                'that\'s', 'it\'s', 'you know', 'basically', 'essentially'
            ]

            if any(answer_lower.startswith(starter) for starter in response_starters):
                score += 0.2

            # Look for answer-like content patterns
            answer_patterns = [
                'because', 'since', 'the reason', 'what happens', 'the answer',
                'you see', 'the thing is', 'it depends', 'in my view'
            ]

            if any(pattern in answer_lower for pattern in answer_patterns):
                score += 0.15

            # Bonus for immediate response (next segment)
            if i == 1:
                score += 0.1

            # Penalty for questions as answers
            if answer_text.strip().endswith('?'):
                score *= 0.5

            best_score = max(best_score, min(1.0, score))

        return best_score

    def _get_answer_text_for_question(self, segments: List[Dict[str, Any]],
                                     question_idx: int, question_speaker: str) -> str:
        """
        Get the answer text for OpenAI enhancement (works without speaker diarization)

        Args:
            segments: All transcript segments
            question_idx: Index of the question segment
            question_speaker: Speaker who asked the question (may be 'unknown')

        Returns:
            Combined answer text from responding segments
        """
        max_lookahead = min(3, len(segments) - question_idx - 1)
        answer_parts = []

        for i in range(1, max_lookahead + 1):
            answer_idx = question_idx + i
            if answer_idx >= len(segments):
                break

            answer_segment = segments[answer_idx]
            answer_speaker = answer_segment.get('speaker', 'unknown')
            answer_text = answer_segment.get('text', '').strip()

            # Skip very short segments
            if len(answer_text) < 10:
                continue

            # Include if different speaker or when no speaker info available
            if question_speaker == 'unknown' or answer_speaker == 'unknown':
                # No speaker info - include if it looks like an answer
                if not answer_text.strip().endswith('?'):  # Not another question
                    answer_parts.append(answer_text)
                else:
                    break  # Stop at next question
            elif answer_speaker != question_speaker:
                # Different speaker - include
                answer_parts.append(answer_text)
            elif len(answer_parts) > 0 and answer_speaker == segments[question_idx + 1].get('speaker'):
                # Continuation of answer from same speaker
                answer_parts.append(answer_text)
            else:
                break  # Stop at speaker change back to questioner

        return ' '.join(answer_parts)

    def calculate_keyword_density(self, text: str, keywords: List[str]) -> float:
        """
        Calculate keyword density using KeyBERT embeddings with OpenAI enhancement

        Args:
            text: Text to analyze
            keywords: List of target keywords

        Returns:
            Keyword density score (0-1)
        """
        if not keywords or not text.strip():
            return 0.0

        # Start with KeyBERT-based scoring
        keybert_score = 0.0
        keybert_model = self._get_keybert_model()
        if keybert_model:
            try:
                # Extract keywords from text using KeyBERT
                extracted_keywords = keybert_model.extract_keywords(
                    text,
                    keyphrase_ngram_range=(1, 2),
                    stop_words='english'
                )[:10]  # Limit to top 10 keywords

                if extracted_keywords:
                    # Calculate similarity between extracted keywords and target keywords
                    extracted_terms = [kw[0] if isinstance(kw, tuple) else str(kw) for kw in extracted_keywords]
                    extracted_scores = [kw[1] if isinstance(kw, tuple) else 1.0 for kw in extracted_keywords]

                    # Simple keyword matching with weights
                    total_score = 0.0
                    for keyword in keywords:
                        keyword_lower = keyword.lower()
                        for i, term in enumerate(extracted_terms):
                            if keyword_lower in term.lower() or term.lower() in keyword_lower:
                                total_score += extracted_scores[i]

                    # Normalize by number of keywords
                    keybert_score = min(total_score / len(keywords), 1.0)

            except Exception as e:
                self.logger.warning(f"Error calculating KeyBERT keyword density: {e}")

        # For cost-effectiveness, only use OpenAI for high-potential spans
        # This will be called later in a batch process for top spans
        return min(1.0, max(0.0, keybert_score))

    def calculate_emotion_intensity(self, text: str, audio_features: Optional[Dict] = None) -> float:
        """
        Calculate emotion intensity using sentiment analysis and audio features

        Args:
            text: Text to analyze
            audio_features: Optional audio features (loudness, prosody, etc.)

        Returns:
            Emotion intensity score (0-1)
        """
        if not text.strip():
            return 0.0

        sentiment_score = 0.0
        audio_score = 0.0

        # Text-based sentiment analysis
        sentiment_model = self._get_sentiment_model()
        if sentiment_model:
            try:
                results = sentiment_model(text)
                if results and len(results) > 0:
                    # Get the highest confidence score
                    max_score = max(result['score'] for result in results[0])
                    sentiment_score = max_score
            except Exception as e:
                self.logger.warning(f"Error in sentiment analysis: {e}")

        # Audio-based emotion features (if available)
        if audio_features:
            try:
                # Use loudness as a proxy for emotional intensity
                loudness = audio_features.get('loudness', -30.0)
                # Normalize loudness to 0-1 scale (assuming range -60 to 0 dB)
                audio_score = max(0.0, min(1.0, (loudness + 60) / 60))
            except Exception as e:
                self.logger.warning(f"Error processing audio features: {e}")

        # Combine text and audio scores
        if audio_features:
            emotion_intensity = 0.7 * sentiment_score + 0.3 * audio_score
        else:
            emotion_intensity = sentiment_score

        return emotion_intensity

    def calculate_novelty_score(self, current_text: str, context_texts: List[str]) -> float:
        """
        Calculate novelty score based on similarity to recent context

        Args:
            current_text: Current text segment
            context_texts: List of previous text segments (60s window)

        Returns:
            Novelty score (0-1, higher = more novel)
        """
        embedding_model = self._get_embedding_model()
        if not embedding_model or not current_text.strip() or not context_texts:
            return 0.5  # Default novelty score

        try:
            # Limit context size to prevent memory issues (max 10 most recent segments)
            limited_context = context_texts[-10:] if len(context_texts) > 10 else context_texts

            if not limited_context:
                return 0.5

            # Generate embeddings
            current_embedding = embedding_model.encode([current_text])
            context_embeddings = embedding_model.encode(limited_context)

            if SKLEARN_AVAILABLE:
                # Import sklearn here for lazy loading
                from sklearn.metrics.pairwise import cosine_similarity
                # Calculate mean similarity to context
                similarities = cosine_similarity(current_embedding, context_embeddings)[0]
                mean_similarity = np.mean(similarities)

                # Novelty is inverse of similarity
                novelty_score = 1.0 - mean_similarity
                return max(0.0, min(1.0, novelty_score))
            else:
                return 0.5

        except Exception as e:
            self.logger.warning(f"Error calculating novelty score: {e}")
            return 0.5

    def apply_quality_filters(self, segment: Dict[str, Any], audio_features: Optional[Dict] = None) -> bool:
        """
        Apply quality filters to determine if segment should be included

        Args:
            segment: Transcript segment with metadata
            audio_features: Optional audio features

        Returns:
            True if segment passes quality filters, False otherwise
        """
        # Check ASR confidence
        confidence = segment.get('confidence', 1.0)
        if confidence < self.quality_thresholds['min_asr_confidence']:
            self.logger.debug(f"Segment filtered: low ASR confidence ({confidence:.2f})")
            return False

        # Check audio loudness (if available)
        if audio_features:
            loudness = audio_features.get('loudness', 0.0)
            if loudness < self.quality_thresholds['min_loudness_lufs']:
                self.logger.debug(f"Segment filtered: low audio loudness ({loudness:.1f} LUFS)")
                return False

        # Check for excessive silence (placeholder - would need audio analysis)
        # This would require actual audio processing to detect silence gaps

        return True

    def calculate_composite_score(self, qa_score: float, keyword_density: float,
                                emotion_intensity: float, novelty: float) -> float:
        """
        Calculate composite score using weighted combination

        Args:
            qa_score: Q&A detection score (0-1)
            keyword_density: Keyword density score (0-1)
            emotion_intensity: Emotion intensity score (0-1)
            novelty: Novelty score (0-1)

        Returns:
            Composite score (0-1)
        """
        composite_score = (
            self.weights['qa_score'] * qa_score +
            self.weights['keyword_density'] * keyword_density +
            self.weights['emotion_intensity'] * emotion_intensity +
            self.weights['novelty'] * novelty
        )

        return min(1.0, max(0.0, composite_score))

    def _enhance_qa_detection_with_openai(self, question_text: str, answer_text: str) -> float:
        """
        Use OpenAI to enhance Q&A pattern detection

        Args:
            question_text: The potential question text
            answer_text: The potential answer text

        Returns:
            Enhanced Q&A score (0-1)
        """
        if not self.openai_enabled or not self.openai_client:
            return 0.0

        if self.openai_request_count >= self.max_openai_requests_per_session:
            return 0.0

        # Create cache key
        cache_key = f"qa_{hash(question_text + answer_text)}"
        if cache_key in self.openai_cache:
            return self.openai_cache[cache_key]

        try:
            prompt = f"""Analyze this dialogue exchange and determine if it represents a meaningful question-answer pattern.

Question: "{question_text}"
Answer: "{answer_text}"

Rate the quality of this Q&A exchange on a scale of 0.0 to 1.0 where:
- 1.0 = Perfect Q&A with clear question and relevant answer
- 0.8 = Good Q&A with minor issues
- 0.6 = Moderate Q&A, somewhat relevant
- 0.4 = Weak Q&A, loosely related
- 0.2 = Poor Q&A, barely related
- 0.0 = Not a Q&A pattern

Respond with only the numerical score (e.g., 0.8)."""

            response = self.openai_client.chat.completions.create(
                model=self.openai_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=10,
                temperature=0.1
            )

            score_text = response.choices[0].message.content.strip()
            score = float(score_text)
            score = max(0.0, min(1.0, score))  # Clamp to valid range

            self.openai_cache[cache_key] = score
            self.openai_request_count += 1

            return score

        except Exception as e:
            self.logger.warning(f"OpenAI Q&A enhancement failed: {e}")
            return 0.0


# Create a global instance for easy access
highlights_scorer = HighlightsScorer()

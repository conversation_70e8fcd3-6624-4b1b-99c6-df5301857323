#!/usr/bin/env python3
"""
Advanced Highlights Scoring Utility for Intelligent Video Highlights

This module implements a sophisticated, self-contained scoring system for video highlights:
- Enhanced Q&A Detection with semantic analysis (35%)
- Advanced Keyword Density using KeyBERT and semantic similarity (25%)
- Multi-model Emotion Intensity via sentiment analysis and engagement detection (25%)
- Improved Novelty scoring with topic diversity analysis (15%)

Key improvements:
- Removed OpenAI dependency for cost-effectiveness and reliability
- Enhanced accuracy through multi-component analysis
- Added semantic similarity and linguistic pattern analysis
- Improved engagement detection with multiple signals
"""

import logging
import numpy as np
from typing import List, Dict, Any, Optional
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)

# Check for library availability without importing heavy dependencies
KEYBERT_AVAILABLE = False
TRANSFORMERS_AVAILABLE = False
SENTENCE_TRANSFORMERS_AVAILABLE = False
SKLEARN_AVAILABLE = False
SCIPY_AVAILABLE = False

try:
    import importlib.util

    # Check KeyBERT availability
    if importlib.util.find_spec("keybert") is not None:
        KEYBERT_AVAILABLE = True

    # Check Transformers availability
    if importlib.util.find_spec("transformers") is not None:
        TRANSFORMERS_AVAILABLE = True

    # Check SentenceTransformers availability
    if importlib.util.find_spec("sentence_transformers") is not None:
        SENTENCE_TRANSFORMERS_AVAILABLE = True

    # Check sklearn availability
    if importlib.util.find_spec("sklearn") is not None:
        SKLEARN_AVAILABLE = True

    # Check scipy availability
    if importlib.util.find_spec("scipy") is not None:
        SCIPY_AVAILABLE = True

except Exception as e:
    logger.warning(f"Error checking library availability: {e}")

# Log availability status
logger.info(f"Library availability - KeyBERT: {KEYBERT_AVAILABLE}, Transformers: {TRANSFORMERS_AVAILABLE}, "
           f"SentenceTransformers: {SENTENCE_TRANSFORMERS_AVAILABLE}, sklearn: {SKLEARN_AVAILABLE}, "
           f"scipy: {SCIPY_AVAILABLE}")


class HighlightsScorer:
    """
    Advanced scoring system for video highlights extraction with enhanced accuracy

    This system uses multiple ML models and sophisticated analysis techniques to
    identify the most engaging video segments without relying on external APIs.
    """

    def __init__(self, embedding_model: str = "all-MiniLM-L6-v2"):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.embedding_model_name = embedding_model

        # Initialize models
        self._init_models()

        # Enhanced scoring weights (maintain the same proportions)
        self.weights = {
            'qa_score': 0.35,
            'keyword_density': 0.25,
            'emotion_intensity': 0.25,
            'novelty': 0.15
        }

        # Quality filter thresholds
        self.quality_thresholds = {
            'min_loudness_lufs': -40.0,
            'min_asr_confidence': 0.85,
            'max_silence_gap': 2.0
        }

        # Enhanced Q&A detection patterns
        self.qa_patterns = self._init_qa_patterns()

        # Engagement detection patterns
        self.engagement_patterns = self._init_engagement_patterns()

        # Semantic similarity cache for performance
        self.similarity_cache = {}

        self.logger.info("Advanced HighlightsScorer initialized successfully")

    def _init_models(self):
        """Initialize ML models with lazy loading"""
        # Set models to None - they will be loaded when first needed
        self.keybert_model = None
        self.sentiment_model = None
        self.embedding_model = None
        self.emotion_model = None  # Additional emotion classification model

        # Track initialization status
        self._keybert_initialized = False
        self._sentiment_initialized = False
        self._embedding_initialized = False
        self._emotion_initialized = False

        self.logger.info("Advanced HighlightsScorer initialized with lazy loading")

    def _init_qa_patterns(self) -> Dict[str, List[str]]:
        """Initialize enhanced Q&A detection patterns"""
        return {
            'question_starters': [
                'what', 'how', 'why', 'when', 'where', 'who', 'which', 'whose',
                'can you', 'could you', 'would you', 'will you', 'should you',
                'do you', 'did you', 'have you', 'are you', 'is it', 'was it',
                'does', 'did', 'can', 'could', 'would', 'will', 'should',
                'is there', 'are there', 'was there', 'were there'
            ],
            'indirect_patterns': [
                'tell me about', 'explain', 'describe', 'elaborate on',
                'what do you think', 'what are your thoughts', 'how do you feel',
                'what would you say', 'what would happen if', 'imagine if',
                'help me understand', 'walk me through', 'break down'
            ],
            'confirmation_patterns': [
                'right?', 'correct?', 'true?', 'agree?', 'isn\'t it?', 'don\'t you think?'
            ],
            'answer_starters': [
                'yes', 'no', 'well', 'so', 'actually', 'i think', 'i believe',
                'absolutely', 'definitely', 'certainly', 'of course', 'sure',
                'that\'s', 'it\'s', 'you know', 'basically', 'essentially',
                'the answer is', 'what happens is', 'the thing is'
            ],
            'answer_patterns': [
                'because', 'since', 'the reason', 'what happens', 'the answer',
                'you see', 'the thing is', 'it depends', 'in my view',
                'from my experience', 'generally speaking', 'typically'
            ]
        }

    def _init_engagement_patterns(self) -> Dict[str, List[str]]:
        """Initialize engagement detection patterns"""
        return {
            'excitement_markers': [
                'amazing', 'incredible', 'fantastic', 'awesome', 'brilliant',
                'wow', 'unbelievable', 'extraordinary', 'remarkable', 'stunning'
            ],
            'emphasis_words': [
                'really', 'very', 'extremely', 'absolutely', 'completely',
                'totally', 'definitely', 'certainly', 'obviously', 'clearly'
            ],
            'surprise_markers': [
                'surprising', 'unexpected', 'shocking', 'believe it or not',
                'turns out', 'interestingly', 'remarkably', 'strangely'
            ],
            'storytelling_markers': [
                'once', 'then', 'suddenly', 'meanwhile', 'afterwards',
                'eventually', 'finally', 'in the end', 'it turned out'
            ]
        }

    def _get_keybert_model(self):
        """Lazy load KeyBERT model"""
        if not self._keybert_initialized:
            self._keybert_initialized = True
            if KEYBERT_AVAILABLE:
                try:
                    from keybert import KeyBERT
                    self.keybert_model = KeyBERT(model=self.embedding_model_name)
                    self.logger.info("KeyBERT model initialized successfully")
                except Exception as e:
                    self.logger.warning(f"Failed to initialize KeyBERT: {e}")
        return self.keybert_model

    def _get_sentiment_model(self):
        """Lazy load sentiment analysis model"""
        if not self._sentiment_initialized:
            self._sentiment_initialized = True
            if TRANSFORMERS_AVAILABLE:
                try:
                    from transformers.pipelines import pipeline
                    self.sentiment_model = pipeline(
                        "sentiment-analysis",
                        model="cardiffnlp/twitter-roberta-base-sentiment-latest",
                        return_all_scores=True
                    )
                    self.logger.info("Sentiment analysis model initialized successfully")
                except Exception as e:
                    self.logger.warning(f"Failed to initialize sentiment model: {e}")
        return self.sentiment_model

    def _get_embedding_model(self):
        """Lazy load sentence transformer model"""
        if not self._embedding_initialized:
            self._embedding_initialized = True
            if SENTENCE_TRANSFORMERS_AVAILABLE:
                try:
                    from sentence_transformers import SentenceTransformer
                    self.embedding_model = SentenceTransformer(self.embedding_model_name)
                    self.logger.info("Sentence transformer model initialized successfully")
                except Exception as e:
                    self.logger.warning(f"Failed to initialize embedding model: {e}")
        return self.embedding_model

    def _get_emotion_model(self):
        """Lazy load emotion classification model"""
        if not self._emotion_initialized:
            self._emotion_initialized = True
            if TRANSFORMERS_AVAILABLE:
                try:
                    from transformers.pipelines import pipeline
                    # Use a more sophisticated emotion model
                    self.emotion_model = pipeline(
                        "text-classification",
                        model="j-hartmann/emotion-english-distilroberta-base",
                        return_all_scores=True
                    )
                    self.logger.info("Emotion classification model initialized successfully")
                except Exception as e:
                    self.logger.warning(f"Failed to initialize emotion model: {e}")
                    # Fallback to basic sentiment if emotion model fails
                    try:
                        self.emotion_model = pipeline(
                            "sentiment-analysis",
                            model="cardiffnlp/twitter-roberta-base-sentiment-latest",
                            return_all_scores=True
                        )
                        self.logger.info("Fallback sentiment model initialized successfully")
                    except Exception as e2:
                        self.logger.warning(f"Failed to initialize fallback sentiment model: {e2}")
        return self.emotion_model

    def detect_qa_patterns(self, segments: List[Dict[str, Any]]) -> Dict[int, float]:
        """
        Detect Q&A patterns in transcript segments with enhanced semantic analysis

        Args:
            segments: List of transcript segments with text and speaker info

        Returns:
            Dictionary mapping segment indices to Q&A scores (0-1)
        """
        qa_scores = {}

        for i, segment in enumerate(segments):
            text = segment.get('text', '').strip()
            speaker = segment.get('speaker', 'unknown')

            # Enhanced question detection patterns
            is_question = self._is_question_segment(text)

            qa_score = 0.0

            if is_question:
                # Look for answer in next 1-3 segments
                answer_score = self._find_answer_for_question(segments, i, speaker)
                qa_score = answer_score

                # Enhance with semantic analysis for high-potential Q&A pairs
                if answer_score > 0.3:
                    answer_text = self._get_answer_text_for_question(segments, i, speaker)
                    if answer_text:
                        semantic_score = self._calculate_qa_semantic_relevance(text, answer_text)
                        # Combine rule-based and semantic scores
                        qa_score = 0.6 * answer_score + 0.4 * semantic_score

                if qa_score > 0.0:
                    self.logger.debug(f"Q&A pattern detected at segment {i}: {text[:50]}... (score: {qa_score:.2f})")

            qa_scores[i] = qa_score

        return qa_scores

    def _calculate_qa_semantic_relevance(self, question_text: str, answer_text: str) -> float:
        """
        Calculate semantic relevance between question and answer using embeddings

        Args:
            question_text: The question text
            answer_text: The answer text

        Returns:
            Semantic relevance score (0-1)
        """
        embedding_model = self._get_embedding_model()
        if not embedding_model or not question_text.strip() or not answer_text.strip():
            return 0.5  # Default score when embeddings unavailable

        try:
            # Create cache key for performance
            cache_key = f"qa_sem_{hash(question_text + answer_text)}"
            if cache_key in self.similarity_cache:
                return self.similarity_cache[cache_key]

            # Generate embeddings
            question_embedding = embedding_model.encode([question_text])
            answer_embedding = embedding_model.encode([answer_text])

            if SKLEARN_AVAILABLE:
                from sklearn.metrics.pairwise import cosine_similarity
                similarity = cosine_similarity(question_embedding, answer_embedding)[0][0]

                # Convert similarity to relevance score (higher similarity = higher relevance)
                relevance_score = max(0.0, min(1.0, similarity))

                # Cache the result
                self.similarity_cache[cache_key] = relevance_score
                return relevance_score
            else:
                return 0.5

        except Exception as e:
            self.logger.warning(f"Error calculating semantic relevance: {e}")
            return 0.5

    def _is_question_segment(self, text: str) -> bool:
        """
        Enhanced question detection using multiple patterns

        Args:
            text: Text to analyze

        Returns:
            True if text appears to be a question
        """
        text_lower = text.lower().strip()

        # Direct question markers
        if text.endswith('?'):
            return True

        # Use enhanced patterns from initialization
        if any(text_lower.startswith(starter) for starter in self.qa_patterns['question_starters']):
            return True

        if any(pattern in text_lower for pattern in self.qa_patterns['indirect_patterns']):
            return True

        # Rising intonation indicators (common in speech-to-text)
        if any(phrase in text_lower for phrase in self.qa_patterns['confirmation_patterns']):
            return True

        return False

    def _find_answer_for_question(self, segments: List[Dict[str, Any]],
                                 question_idx: int, question_speaker: str) -> float:
        """
        Find and score potential answers following a question (works without speaker diarization)

        Args:
            segments: All transcript segments
            question_idx: Index of the question segment
            question_speaker: Speaker who asked the question (may be 'unknown')

        Returns:
            Score indicating quality of Q&A pattern (0-1)
        """
        max_lookahead = min(3, len(segments) - question_idx - 1)
        best_score = 0.0

        for i in range(1, max_lookahead + 1):
            answer_idx = question_idx + i
            if answer_idx >= len(segments):
                break

            answer_segment = segments[answer_idx]
            answer_speaker = answer_segment.get('speaker', 'unknown')
            answer_text = answer_segment.get('text', '').strip()

            # Skip if very short response
            if len(answer_text) < 10:
                continue

            # Skip if same speaker (only when we have speaker info)
            if (question_speaker != 'unknown' and answer_speaker != 'unknown' and
                answer_speaker == question_speaker):
                continue

            # Score based on answer quality indicators
            score = 0.0

            # Different speaker responding = good (when we have speaker info)
            if (answer_speaker != 'unknown' and question_speaker != 'unknown' and
                answer_speaker != question_speaker):
                score += 0.4
            elif answer_speaker == 'unknown' or question_speaker == 'unknown':
                # Base score when no speaker info available
                score += 0.2

            # Answer length indicates substantive response (more important without speaker info)
            if len(answer_text) > 50:
                score += 0.4
            elif len(answer_text) > 30:
                score += 0.3
            elif len(answer_text) > 15:
                score += 0.2

            # Answer starts with typical response patterns (use enhanced patterns)
            answer_lower = answer_text.lower()
            if any(answer_lower.startswith(starter) for starter in self.qa_patterns['answer_starters']):
                score += 0.2

            # Look for answer-like content patterns (use enhanced patterns)
            if any(pattern in answer_lower for pattern in self.qa_patterns['answer_patterns']):
                score += 0.15

            # Bonus for immediate response (next segment)
            if i == 1:
                score += 0.1

            # Penalty for questions as answers
            if answer_text.strip().endswith('?'):
                score *= 0.5

            best_score = max(best_score, min(1.0, score))

        return best_score

    def _get_answer_text_for_question(self, segments: List[Dict[str, Any]],
                                     question_idx: int, question_speaker: str) -> str:
        """
        Get the answer text for OpenAI enhancement (works without speaker diarization)

        Args:
            segments: All transcript segments
            question_idx: Index of the question segment
            question_speaker: Speaker who asked the question (may be 'unknown')

        Returns:
            Combined answer text from responding segments
        """
        max_lookahead = min(3, len(segments) - question_idx - 1)
        answer_parts = []

        for i in range(1, max_lookahead + 1):
            answer_idx = question_idx + i
            if answer_idx >= len(segments):
                break

            answer_segment = segments[answer_idx]
            answer_speaker = answer_segment.get('speaker', 'unknown')
            answer_text = answer_segment.get('text', '').strip()

            # Skip very short segments
            if len(answer_text) < 10:
                continue

            # Include if different speaker or when no speaker info available
            if question_speaker == 'unknown' or answer_speaker == 'unknown':
                # No speaker info - include if it looks like an answer
                if not answer_text.strip().endswith('?'):  # Not another question
                    answer_parts.append(answer_text)
                else:
                    break  # Stop at next question
            elif answer_speaker != question_speaker:
                # Different speaker - include
                answer_parts.append(answer_text)
            elif len(answer_parts) > 0 and answer_speaker == segments[question_idx + 1].get('speaker'):
                # Continuation of answer from same speaker
                answer_parts.append(answer_text)
            else:
                break  # Stop at speaker change back to questioner

        return ' '.join(answer_parts)

    def calculate_keyword_density(self, text: str, keywords: List[str]) -> float:
        """
        Calculate keyword density using KeyBERT embeddings with OpenAI enhancement

        Args:
            text: Text to analyze
            keywords: List of target keywords

        Returns:
            Keyword density score (0-1)
        """
        if not keywords or not text.strip():
            return 0.0

        # Start with KeyBERT-based scoring
        keybert_score = 0.0
        keybert_model = self._get_keybert_model()
        if keybert_model:
            try:
                # Extract keywords from text using KeyBERT
                extracted_keywords = keybert_model.extract_keywords(
                    text,
                    keyphrase_ngram_range=(1, 2),
                    stop_words='english'
                )[:10]  # Limit to top 10 keywords

                if extracted_keywords:
                    # Calculate similarity between extracted keywords and target keywords
                    extracted_terms = [kw[0] if isinstance(kw, tuple) else str(kw) for kw in extracted_keywords]
                    extracted_scores = [kw[1] if isinstance(kw, tuple) else 1.0 for kw in extracted_keywords]

                    # Simple keyword matching with weights
                    total_score = 0.0
                    for keyword in keywords:
                        keyword_lower = keyword.lower()
                        for i, term in enumerate(extracted_terms):
                            if keyword_lower in term.lower() or term.lower() in keyword_lower:
                                total_score += extracted_scores[i]

                    # Normalize by number of keywords
                    keybert_score = min(total_score / len(keywords), 1.0)

            except Exception as e:
                self.logger.warning(f"Error calculating KeyBERT keyword density: {e}")

        # For cost-effectiveness, only use OpenAI for high-potential spans
        # This will be called later in a batch process for top spans
        return min(1.0, max(0.0, keybert_score))

    def calculate_emotion_intensity(self, text: str, audio_features: Optional[Dict] = None) -> float:
        """
        Calculate emotion intensity using enhanced sentiment analysis and engagement detection

        Args:
            text: Text to analyze
            audio_features: Optional audio features (loudness, prosody, etc.)

        Returns:
            Emotion intensity score (0-1)
        """
        if not text.strip():
            return 0.0

        sentiment_score = 0.0
        engagement_score = 0.0
        audio_score = 0.0

        # Enhanced text-based sentiment analysis
        sentiment_model = self._get_sentiment_model()
        if sentiment_model:
            try:
                results = sentiment_model(text)
                if results and isinstance(results, list) and len(results) > 0:
                    # Handle different model output formats
                    if isinstance(results[0], list):
                        # Multiple scores format
                        max_score = max(result['score'] for result in results[0])
                        sentiment_score = max_score
                    else:
                        # Single score format
                        sentiment_score = results[0].get('score', 0.0)
            except Exception as e:
                self.logger.warning(f"Error in sentiment analysis: {e}")

        # Enhanced engagement detection using patterns
        engagement_score = self._calculate_engagement_score(text)

        # Audio-based emotion features (if available)
        if audio_features:
            try:
                # Use loudness as a proxy for emotional intensity
                loudness = audio_features.get('loudness', -30.0)
                # Normalize loudness to 0-1 scale (assuming range -60 to 0 dB)
                audio_score = max(0.0, min(1.0, (loudness + 60) / 60))
            except Exception as e:
                self.logger.warning(f"Error processing audio features: {e}")

        # Combine sentiment, engagement, and audio scores
        if audio_features:
            emotion_intensity = 0.5 * sentiment_score + 0.3 * engagement_score + 0.2 * audio_score
        else:
            emotion_intensity = 0.7 * sentiment_score + 0.3 * engagement_score

        return min(1.0, max(0.0, emotion_intensity))

    def _calculate_engagement_score(self, text: str) -> float:
        """
        Calculate engagement score based on linguistic patterns

        Args:
            text: Text to analyze

        Returns:
            Engagement score (0-1)
        """
        if not text.strip():
            return 0.0

        text_lower = text.lower()
        engagement_score = 0.0

        # Check for excitement markers
        excitement_count = sum(1 for marker in self.engagement_patterns['excitement_markers']
                             if marker in text_lower)
        engagement_score += min(0.3, excitement_count * 0.1)

        # Check for emphasis words
        emphasis_count = sum(1 for word in self.engagement_patterns['emphasis_words']
                           if word in text_lower)
        engagement_score += min(0.2, emphasis_count * 0.05)

        # Check for surprise markers
        surprise_count = sum(1 for marker in self.engagement_patterns['surprise_markers']
                           if marker in text_lower)
        engagement_score += min(0.2, surprise_count * 0.1)

        # Check for storytelling markers
        story_count = sum(1 for marker in self.engagement_patterns['storytelling_markers']
                        if marker in text_lower)
        engagement_score += min(0.15, story_count * 0.05)

        # Check for exclamation marks and caps
        if '!' in text:
            engagement_score += 0.1
        if any(word.isupper() and len(word) > 2 for word in text.split()):
            engagement_score += 0.05

        return min(1.0, engagement_score)

    def calculate_novelty_score(self, current_text: str, context_texts: List[str]) -> float:
        """
        Calculate novelty score based on similarity to recent context

        Args:
            current_text: Current text segment
            context_texts: List of previous text segments (60s window)

        Returns:
            Novelty score (0-1, higher = more novel)
        """
        embedding_model = self._get_embedding_model()
        if not embedding_model or not current_text.strip() or not context_texts:
            return 0.5  # Default novelty score

        try:
            # Limit context size to prevent memory issues (max 10 most recent segments)
            limited_context = context_texts[-10:] if len(context_texts) > 10 else context_texts

            if not limited_context:
                return 0.5

            # Generate embeddings
            current_embedding = embedding_model.encode([current_text])
            context_embeddings = embedding_model.encode(limited_context)

            if SKLEARN_AVAILABLE:
                # Import sklearn here for lazy loading
                from sklearn.metrics.pairwise import cosine_similarity
                # Calculate mean similarity to context
                similarities = cosine_similarity(current_embedding, context_embeddings)[0]
                mean_similarity = np.mean(similarities)

                # Novelty is inverse of similarity
                novelty_score = 1.0 - mean_similarity
                return max(0.0, min(1.0, novelty_score))
            else:
                return 0.5

        except Exception as e:
            self.logger.warning(f"Error calculating novelty score: {e}")
            return 0.5

    def apply_quality_filters(self, segment: Dict[str, Any], audio_features: Optional[Dict] = None) -> bool:
        """
        Apply quality filters to determine if segment should be included

        Args:
            segment: Transcript segment with metadata
            audio_features: Optional audio features

        Returns:
            True if segment passes quality filters, False otherwise
        """
        # Check ASR confidence
        confidence = segment.get('confidence', 1.0)
        if confidence < self.quality_thresholds['min_asr_confidence']:
            self.logger.debug(f"Segment filtered: low ASR confidence ({confidence:.2f})")
            return False

        # Check audio loudness (if available)
        if audio_features:
            loudness = audio_features.get('loudness', 0.0)
            if loudness < self.quality_thresholds['min_loudness_lufs']:
                self.logger.debug(f"Segment filtered: low audio loudness ({loudness:.1f} LUFS)")
                return False

        # Check for excessive silence (placeholder - would need audio analysis)
        # This would require actual audio processing to detect silence gaps

        return True

    def calculate_composite_score(self, qa_score: float, keyword_density: float,
                                emotion_intensity: float, novelty: float) -> float:
        """
        Calculate composite score using weighted combination

        Args:
            qa_score: Q&A detection score (0-1)
            keyword_density: Keyword density score (0-1)
            emotion_intensity: Emotion intensity score (0-1)
            novelty: Novelty score (0-1)

        Returns:
            Composite score (0-1)
        """
        composite_score = (
            self.weights['qa_score'] * qa_score +
            self.weights['keyword_density'] * keyword_density +
            self.weights['emotion_intensity'] * emotion_intensity +
            self.weights['novelty'] * novelty
        )

        return min(1.0, max(0.0, composite_score))

# Create a global instance for easy access
highlights_scorer = HighlightsScorer()
